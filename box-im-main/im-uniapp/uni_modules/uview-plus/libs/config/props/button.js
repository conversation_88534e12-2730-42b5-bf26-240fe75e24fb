/*
 * <AUTHOR> LQ
 * @Description  :
 * @version      : 1.0
 * @Date         : 2021-08-20 16:44:21
 * @LastAuthor   : LQ
 * @lastTime     : 2021-08-20 16:51:27
 * @FilePath     : /u-view2.0/uview-ui/libs/config/props/button.js
 */
export default {
    // button组件
    button: {
        hairline: false,
        type: 'info',
        size: 'normal',
        shape: 'square',
        plain: false,
        disabled: false,
        loading: false,
        loadingText: '',
        loadingMode: 'spinner',
        loadingSize: 15,
        openType: '',
        formType: '',
        appParameter: '',
        hoverStopPropagation: true,
        lang: 'en',
        sessionFrom: '',
        sendMessageTitle: '',
        sendMessagePath: '',
        sendMessageImg: '',
        showMessageCard: false,
        dataName: '',
        throttleTime: 0,
        hoverStartTime: 0,
        hoverStayTime: 200,
        text: '',
        icon: '',
        iconColor: '',
        color: ''
    }
}
