<template>
  <view class="bom-scan-page">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-left" @click="goBack">
        <text class="back-icon">←</text>
      </view>
      <view class="nav-title">根据BOM清单扫码领料</view>
      <view class="nav-right"></view>
    </view>

    <!-- 物料信息卡片 -->
    <view class="material-info-card">
      <view class="info-row">
        <text class="info-label">物料名称:</text>
        <text class="info-value">{{ itemName || '-' }}</text>
      </view>
      <view class="info-row">
        <text class="info-label">需求数量:</text>
        <text class="info-value">{{ sumQuantity || '-' }}</text>
      </view>
<!--      <view class="info-row">-->
<!--        <text class="info-label">工序任务ID:</text>-->
<!--        <text class="info-value">{{ stepTaskId || '-' }}</text>-->
<!--      </view>-->
    </view>

    <!-- 扫码区域 -->
    <view class="scan-section">
      <view class="scan-card">
        <view class="scan-icon-container" @click="startScan">
          <text class="scan-icon">📷</text>
          <text class="scan-text">点击扫描二维码</text>
        </view>

        <!-- 扫码结果显示 -->
        <view v-if="scanResult" class="scan-result">
          <view class="result-title">扫码结果:</view>
          <view class="result-item">
            <text class="result-label">采购单号:</text>
            <text class="result-value">{{ scanResult.purchaseNo || '-' }}</text>
          </view>
          <view v-if="scanResult.batchNo" class="result-item">
            <text class="result-label">批次号:</text>
            <text class="result-value">{{ scanResult.batchNo }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <view
        class="action-btn confirm-btn"
        :class="{ disabled: !canConfirm }"
        @click="confirmOutbound"
      >
        <text class="btn-text">确认出库</text>
      </view>
      <view class="action-btn cancel-btn" @click="goBack">
        <text class="btn-text">取消</text>
      </view>
    </view>
  </view>
</template>

<script>
import { outboundSemiFinishedProductBom, showMessage } from '@/api/workOrders.js'

export default {
  name: "bomScan",
  data() {
    return {
      // 页面参数
      itemName: '',        // 物料名称
      sumQuantity: '',     // 总数量
      stepTaskId: '',      // 步骤任务ID

      // 扫码结果
      scanResult: null,    // 扫码解析结果

      // 状态管理
      loading: false
    }
  },

  computed: {
    // 是否可以确认出库
    canConfirm() {
      return this.scanResult &&
             this.scanResult.purchaseNo &&
             this.itemName &&
             this.sumQuantity &&
             this.stepTaskId
    }
  },

  onLoad(options) {
    // 接收页面参数
    this.itemName = decodeURIComponent(options.itemName || '')
    this.sumQuantity = options.sumQuantity || ''
    this.stepTaskId = options.stepTaskId || ''

    console.log('bomScan页面参数:', {
      itemName: this.itemName,
      sumQuantity: this.sumQuantity,
      stepTaskId: this.stepTaskId
    })
  },

  methods: {
    /** 返回上一页 */
    goBack() {
      uni.navigateBack()
    },

    /** 开始扫码 */
    startScan() {
      console.log('开始扫码')

      // 调用uni-app扫码API
      uni.scanCode({
        scanType: ['qrCode', 'barCode'], // 支持二维码和条形码
        success: (res) => {
          console.log('扫码成功，结果:', res.result)
          // 扫码成功，处理扫码结果
          this.processScanResult(res.result)
        },
        fail: (err) => {
          console.error('扫码失败:', err)
          // 扫码失败处理
          this.handleScanError(err)
        }
      })
    },

    /** 处理扫码结果 */
    processScanResult(scanData) {
      try {
        console.log('处理扫码结果:', scanData)

        // 解析扫码数据，提取 purchase_no 和 batch_no
        const result = this.parseScanData(scanData)
        console.log('解析出的扫码数据:', result)

        if (!result.purchaseNo) {
          uni.showToast({
            title: '扫码数据格式错误，无法解析采购单号',
            icon: 'none',
            duration: 3000
          })
          return
        }

        // 保存扫码结果
        this.scanResult = result

        uni.showToast({
          title: '扫码成功',
          icon: 'success',
          duration: 2000
        })

      } catch (error) {
        console.error('处理扫码结果失败:', error)
        uni.showToast({
          title: '处理扫码结果失败: ' + (error.message || '未知错误'),
          icon: 'none',
          duration: 3000
        })
      }
    },

    /** 解析扫码数据，提取 purchase_no 和 batch_no */
    parseScanData(scanData) {
      try {
        // 扫码数据格式示例：label_type:purchase|purchase_no:PO20250731006|batch_no:20250731-003
        console.log('开始解析扫码数据:', scanData)

        const result = {
          purchaseNo: null,
          batchNo: null
        }

        if (!scanData || typeof scanData !== 'string') {
          console.warn('扫码数据为空或格式不正确')
          return result
        }

        // 使用正则表达式提取 purchase_no 和 batch_no
        const purchaseNoMatch = scanData.match(/purchase_no:([^|]+)/i)
        const batchNoMatch = scanData.match(/batch_no:([^|]+)/i)

        if (purchaseNoMatch && purchaseNoMatch[1]) {
          result.purchaseNo = purchaseNoMatch[1].trim()
          console.log('正则匹配解析出的purchase_no:', result.purchaseNo)
        }

        if (batchNoMatch && batchNoMatch[1]) {
          result.batchNo = batchNoMatch[1].trim()
          console.log('正则匹配解析出的batch_no:', result.batchNo)
        }

        // 如果正则匹配失败，尝试手动解析
        if (!result.purchaseNo || !result.batchNo) {
          console.log('正则匹配部分失败，尝试手动解析')
          const parts = scanData.split('|')

          for (const part of parts) {
            const trimmedPart = part.trim()

            if (!result.purchaseNo && trimmedPart.includes('purchase_no:')) {
              const purchaseNo = trimmedPart.split('purchase_no:')[1]?.trim()
              if (purchaseNo) {
                result.purchaseNo = purchaseNo
                console.log('手动解析出的purchase_no:', result.purchaseNo)
              }
            }

            if (!result.batchNo && trimmedPart.includes('batch_no:')) {
              const batchNo = trimmedPart.split('batch_no:')[1]?.trim()
              if (batchNo) {
                result.batchNo = batchNo
                console.log('手动解析出的batch_no:', result.batchNo)
              }
            }
          }
        }

        // 如果仍然没有解析出 purchase_no，检查是否整个字符串就是采购单号
        if (!result.purchaseNo && scanData.startsWith('PO') && scanData.length > 5) {
          result.purchaseNo = scanData.trim()
          console.log('直接使用扫码数据作为purchase_no:', result.purchaseNo)
        }

        console.log('最终解析结果:', result)
        return result
      } catch (error) {
        console.error('解析扫码数据失败:', error)
        return {
          purchaseNo: null,
          batchNo: null
        }
      }
    },

    /** 处理扫码失败 */
    handleScanError(error) {
      console.error('扫码失败:', error)

      let errorMessage = '扫码失败'

      if (error.errMsg) {
        if (error.errMsg.includes('cancel')) {
          errorMessage = '用户取消扫码'
        } else if (error.errMsg.includes('fail')) {
          errorMessage = '扫码功能异常，请重试'
        }
      }

      uni.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000
      })
    },

    /** 确认出库操作 */
    async confirmOutbound() {
      if (!this.canConfirm) {
        uni.showToast({
          title: '请先扫码获取采购单号',
          icon: 'none',
          duration: 2000
        })
        return
      }

      // 显示确认对话框
      const confirmed = await this.showConfirmDialog()
      if (!confirmed) {
        return
      }

      // 执行出库操作
      await this.performOutbound()
    },

    /** 显示确认对话框 */
    showConfirmDialog() {
      return new Promise((resolve) => {
        let content = `确定要出库以下物料吗？\n\n物料名称：${this.itemName}\n数量：${this.sumQuantity}\n采购单号：${this.scanResult.purchaseNo}`

        if (this.scanResult.batchNo) {
          content += `\n批次号：${this.scanResult.batchNo}`
        }

        uni.showModal({
          title: '确认出库',
          content: content,
          confirmText: '确定',
          cancelText: '取消',
          success: (res) => {
            resolve(res.confirm)
          },
          fail: () => {
            resolve(false)
          }
        })
      })
    },

    /** 执行出库操作 */
    async performOutbound() {
      try {
        // 显示加载提示
        uni.showLoading({
          title: '出库中...'
        })

        // 构建请求参数
        const formData = {
          quantity: this.sumQuantity,              // 总数量
          purchaseNo: this.scanResult.purchaseNo,  // 采购单号
          itemName: this.itemName,                 // 物料名称
          stepTaskId: this.stepTaskId              // 步骤任务ID
        }

        console.log('BOM物料出库API调用参数:', formData)

        // 调用出库API
        const response = await outboundSemiFinishedProductBom(formData)

        console.log('BOM物料出库API响应:', response)

        uni.hideLoading()

        // 根据WMS API规范，成功响应的code应该是0
        if (response && (response.code === 0 || response.code === 200)) {
          // 显示成功提示
          uni.showToast({
            title: '物料出库成功',
            icon: 'success',
            duration: 2000
          })

          // 延迟返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 2000)

        } else {
          console.error('BOM物料出库失败详情:', {
            code: response?.code,
            message: response?.message,
            data: response?.data
          })

          // 显示后端返回的具体错误原因
          const errorMessage = response?.message || response?.msg || '物料出库失败，请稍后重试'
          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 5000
          })
        }

      } catch (error) {
        uni.hideLoading()

        console.error('BOM物料出库操作异常:', error)

        // 处理不同类型的错误
        let errorMessage = '网络请求失败，请检查网络连接'

        if (error.message) {
          if (error.message.includes('timeout')) {
            errorMessage = '请求超时，请稍后重试'
          } else if (error.message.includes('Network')) {
            errorMessage = '网络连接失败，请检查网络'
          } else {
            errorMessage = error.message
          }
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 5000
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.bom-scan-page {
  margin-top: 45rpx;
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e8e8e8;
  padding: 0 20rpx;
  box-sizing: border-box;

  .nav-left {
    width: 80rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .back-icon {
      font-size: 36rpx;
      color: #007aff;
      font-weight: bold;
    }
  }

  .nav-title {
    flex: 1;
    text-align: center;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }

  .nav-right {
    width: 80rpx;
  }
}

/* 物料信息卡片 */
.material-info-card {
  background-color: #fff;
  margin: 20rpx 16rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  .info-row {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      font-size: 28rpx;
      color: #666;
      width: 160rpx;
      flex-shrink: 0;
    }

    .info-value {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
      flex: 1;
    }
  }
}

/* 扫码区域 */
.scan-section {
  flex: 1;
  padding: 20rpx 16rpx;
  display: flex;
  flex-direction: column;

  .scan-card {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 40rpx 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    text-align: center;

    .scan-icon-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60rpx 0;
      border: 2rpx dashed #ddd;
      border-radius: 16rpx;
      background-color: #fafafa;

      .scan-icon {
        font-size: 120rpx;
        margin-bottom: 20rpx;
      }

      .scan-text {
        font-size: 28rpx;
        color: #666;
      }
    }

    .scan-result {
      margin-top: 30rpx;
      padding: 20rpx;
      background-color: #f8f9fa;
      border-radius: 12rpx;
      text-align: left;

      .result-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 16rpx;
      }

      .result-item {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .result-label {
          font-size: 26rpx;
          color: #666;
          width: 120rpx;
          flex-shrink: 0;
        }

        .result-value {
          font-size: 26rpx;
          color: #333;
          font-weight: 500;
          flex: 1;
        }
      }
    }
  }
}

/* 操作按钮区域 */
.action-section {
  padding: 20rpx 16rpx 40rpx;
  display: flex;
  gap: 20rpx;

  .action-btn {
    flex: 1;
    height: 88rpx;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .btn-text {
      font-size: 32rpx;
      font-weight: 600;
    }

    &.confirm-btn {
      background-color: #4caf50;
      color: #fff;

      &.disabled {
        background-color: #ccc;
        color: #999;
      }
    }

    &.cancel-btn {
      background-color: #f5f5f5;
      color: #666;
      border: 1rpx solid #ddd;
    }
  }
}
</style>