<template>
  <view class="page my-order-task">
    <nav-bar back>我的任务</nav-bar>

    <!-- 状态标签页 - 固定在顶部 -->
    <view class="tab-bar-fixed">
      <view class="tab-bar">
        <view
            v-for="(tab, index) in statusTabs"
            :key="tab.value"
            class="tab-item"
            :class="{ active: currentTab === tab.value }"
            @click="switchTab(tab.value)"
        >
          <text class="tab-text">{{ tab.label }}</text>
          <view v-if="currentTab === tab.value" class="tab-indicator"></view>
        </view>
      </view>
    </view>

    <view class="content">
      <!-- 任务列表 -->
      <scroll-view
          class="task-list"
          scroll-y="true"
          @scrolltolower="loadMore"
      >
      <!-- 加载状态 -->
      <view v-if="loading && taskList.length === 0" class="loading-container">
        <u-loading-icon mode="spinner" size="40"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 空状态 -->
      <view v-else-if="!loading && taskList.length === 0" class="empty-container">
        <u-empty
            mode="data"
            text="暂无任务数据"
            textColor="#999"
            iconSize="120"
        ></u-empty>
      </view>

      <!-- 工单卡片列表 -->
      <view v-else class="order-cards">
        <view
            v-for="(order, index) in taskList"
            :key="`${order.orderId}-${index}`"
            class="order-card"
            @click="toggleProductsPreview(order)"
        >
          <!-- 工单基本信息 -->
          <view class="card-header">
            <view class="order-info">
              <text class="order-code">{{ order.orderCode }}</text>
              <view class="status-badge" :class="getStatusClass(order.orderStatus)">
                {{ getStatusText(order.orderStatus) }}
              </view>
            </view>
            <view class="order-type-badge" :class="getOrderTypeClass(order.orderType)">
              {{ getOrderTypeText(order.orderType) }}
            </view>
          </view>

          <!-- 工单统计信息 -->
          <view class="card-content">
            <view class="stats-info">
              <view class="stat-item">
                <text class="stat-label">产品数量</text>
                <text class="stat-value">{{ order.productCount }}</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">总步骤</text>
                <text class="stat-value">{{ order.totalStepTasks }}</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">已完成</text>
                <text class="stat-value completed">{{ order.completedStepTasks }}</text>
              </view>
              <view class="stat-item">
                <text class="stat-label">进度</text>
                <text class="stat-value progress">
                  {{ order.totalStepTasks > 0 ? Math.round((order.completedStepTasks / order.totalStepTasks) * 100) : 0 }}%
                </text>
              </view>
            </view>

            <!-- 进度条 -->
            <view class="progress-bar">
              <view class="progress-track">
                <view
                    class="progress-fill"
                    :style="{ width: (order.totalStepTasks > 0 ? (order.completedStepTasks / order.totalStepTasks) * 100 : 0) + '%' }"
                ></view>
              </view>
            </view>

            <!-- 产品预览控制按钮 -->
            <view v-if="order.products && order.products.length > 0" class="products-toggle">
              <view class="toggle-button" @click.stop="toggleProductsPreview(order)">
                <text class="toggle-text">产品信息</text>
                <view class="toggle-icon" :class="{ 'rotated': order.showProductsPreview }">
                  <text class="arrow-down">▼</text>
                </view>
              </view>
            </view>

            <!-- 产品预览 -->
            <view v-if="order.products && order.products.length > 0 && order.showProductsPreview" class="products-preview">
              <view class="product-list">
                <view
                    v-for="(product, productIndex) in order.products"
                    :key="`${product.orderItemId}-${productIndex}`"
                    class="product-item"
                    :class="{ 'expanded': order.expandedProducts && order.expandedProducts[productIndex] }"
                    @click.stop="toggleProductDetail(order, productIndex)"
                >
                  <!-- 产品基本信息 -->
                  <view class="product-header">
                    <view class="product-info">
                      <text class="product-name">{{ product.productName || '未知产品' }}</text>
                      <text class="product-style">{{ product.styleName || '-' }}~{{product.boardType}}</text>
                      <text class="product-quantity">数量: {{ product.orderItemQuantity || 0 }}</text>
                    </view>
                    <view class="product-actions">
                      <!-- 库存信息按钮 -->
                      <view class="inventory-button" @click.stop="showInventoryDialog(order, productIndex, product)">
                        <text class="inventory-label">库存信息</text>
                        <view class="inventory-icon" :class="{ 'loading': isInventoryLoading(order, productIndex) }">
                          <u-loading-icon v-if="isInventoryLoading(order, productIndex)" mode="spinner" size="16"></u-loading-icon>
                          <text v-else class="info-icon">ⓘ</text>
                        </view>
                      </view>
                      <view class="bom-button" @click.stop="viewBom(product)">
                        <text class="bom-text">查看BOM</text>
                      </view>
                      <view class="expand-icon" :class="{ 'rotated': order.expandedProducts && order.expandedProducts[productIndex] }">
                        <text class="arrow-down">▼</text>
                      </view>
                    </view>
                  </view>

                  <!-- 产品详细信息（工序步骤） -->
                  <view v-if="order.expandedProducts && order.expandedProducts[productIndex]" class="product-details">
                    <view class="product-meta">
                      <!--                      <text class="meta-item">产品ID: {{ product.productId }}</text>-->
                      <!--                      <text class="meta-item">工艺路线: {{ product.processRouteCode }}</text>-->
                      <!--                      <text class="meta-item">款式: {{ product.styleName }}</text>-->
                      <!--                      <text class="meta-item">数量: {{ product.orderItemQuantity }}</text>-->
                    </view>

                    <!-- 工序步骤列表 -->
                    <view v-if="product.stepTasks && product.stepTasks.length > 0" class="step-tasks">
                      <text class="step-title">工序步骤</text>
                      <view class="step-list">
                        <view
                            v-for="(step, stepIndex) in product.stepTasks"
                            :key="`${step.stepTaskId}-${stepIndex}`"
                            class="step-item"
                            :class="getStepStatusClass(step.isCompleted)"
                            @click.stop
                        >
                          <view class="step-header">
                            <view class="step-info">
                              <text class="step-name">{{ step.stepName }}</text>
<!--                              <text class="step-number">{{ step.stepNumber }}</text>-->
                            </view>
                            <view class="step-right">
                              <view class="step-status-badge" :class="getStepStatusClass(step.isCompleted)">
                                {{ getStepStatusText(step.isCompleted) }}
                              </view>

                              <!-- 动态按钮区域 -->
                              <view class="step-actions">

                                <!-- 未开始状态：显示"开始"按钮，但工单状态为暂停中时不显示，且当工序为"领取PCB裸板"或"分类清点数量入库"时不显示 -->
                                <view
                                    v-if="step.isCompleted === 0 && order.orderStatus !== 'PAUSED' && step.stepName !== '领取PCB裸板' && step.stepName !== '分类清点数量入库'"
                                    class="step-action-btn start-btn"
                                    :class="{ 'loading': isStepLoading(step.stepTaskId) }"
                                    @click.stop="confirmStartStep(order, product, step)"
                                >
                                  <text v-if="isStepLoading(step.stepTaskId)" class="loading-text">开始中...</text>
                                  <text v-else>开始</text>
                                </view>

                                <!-- 领料按钮：当工序步骤为"领取PCB裸板"时显示，但工单状态为暂停中时不显示 -->
                                <view
                                  v-if="step.stepName === '领取PCB裸板' && order.orderStatus !== 'PAUSED'"
                                  class="step-action-btn material-btn"
                                  @click.stop="handleMaterialPicking(order, product, step)"
                                >
                                  <text>领料</text>
                                </view>

                                <!-- 申领半成品PCB板及零件的领料按钮 -->
                                <view
                                  v-if="step.stepName === '申领半成品PCB板及零件' && order.orderStatus !== 'PAUSED'"
                                  class="step-action-btn material-btn"
                                  @click.stop="handleSemifinshedMaterialPicking(order, product, step)"
                                >
                                  <text>领料</text>
                                </view>

                                <!-- 测试员领取焊接半成品的领料按钮 -->
                                <view
                                    v-if="step.stepName === '测试员领取焊接半成品' && order.orderStatus !== 'PAUSED'"
                                    class="step-action-btn material-btn"
                                    @click.stop="handleSemifinshedProductThree(order, product, step)"
                                >
                                  <text>领料</text>
                                </view>

                                <!-- 执行中状态：显示"完成"按钮，但工单状态为暂停中时不显示 -->
                                <view
                                  v-if="step.isCompleted === 1 && order.orderStatus !== 'PAUSED'"
                                  class="step-action-btn complete-btn"
                                  :class="{ 'loading': isStepLoading(step.stepTaskId) }"
                                  @click.stop="confirmCompleteStep(order, product, step)"
                                >
                                  <text v-if="isStepLoading(step.stepTaskId)" class="loading-text">完成中...</text>
                                  <text v-else>完成</text>
                                </view>

                                <!-- 入库按钮：当工序步骤为"分类清点数量入库"时显示，但工单状态为暂停中时不显示 -->
                                <view
                                  v-if="step.stepName === '分类清点数量入库' && order.orderStatus !== 'PAUSED'"
                                  class="step-action-btn storage-btn"
                                  @click.stop="handleStorageScan(order, product, step)"
                                >
                                  <text>入库</text>
                                </view>

                                <view
                                    v-if="step.stepName === '清点上下板数量入线边仓' && order.orderStatus !== 'PAUSED'"
                                    class="step-action-btn storage-btn"
                                    @click.stop="handleStorageScanTwo(order, product, step)"
                                >
                                  <text>入库</text>
                                </view>

                                <view
                                    v-if="step.stepName === '产品入库' && order.orderStatus !== 'PAUSED'"
                                    class="step-action-btn storage-btn"
                                    @click.stop="handleStorageScanThree(order, product, step)"
                                >
                                  <text>入库</text>
                                </view>


                                <!-- 执行中和已完成状态：显示"提交缺陷"按钮，但工单状态为已完成或暂停中时不显示 -->
                                <view
                                  v-if="(step.isCompleted === 1 || step.isCompleted === 2) && order.orderStatus !== 'COMPLETED' && order.orderStatus !== 'PAUSED'"
                                  class="step-action-btn defect-btn"
                                  @click.stop="handleSubmitDefect(order, product, step)"
                                >
                                  <text>提交缺陷</text>
                                </view>

                                <!-- 已完成状态：不显示完成按钮 -->
                              </view>
                            </view>
                          </view>
                          <view class="step-details">
<!--                            <text class="step-assignee">负责人: {{ step.assignee || '-' }}</text>-->
                            <text class="step-expected">预期完成: {{ formatTime(step.expectedAt) }}</text>
                            <text v-if="step.completedAt" class="step-completed">完成时间: {{ formatTime(step.completedAt) }}</text>
                          </view>
                        </view>
                      </view>
                    </view>
                    <view v-else class="no-steps">
                      <text class="no-steps-text">暂无工序步骤</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && taskList.length > 0" class="load-more">
        <u-loading-icon v-if="loadingMore" mode="spinner" size="24"></u-loading-icon>
        <text class="load-more-text">{{ loadingMore ? '加载中...' : '上拉加载更多' }}</text>
      </view>

        <!-- 没有更多数据 -->
        <view v-if="!hasMore && taskList.length > 0" class="no-more">
          <text class="no-more-text">没有更多数据了</text>
        </view>
      </scroll-view>
    </view>

    <!-- 库存信息对话框 -->
    <uni-popup ref="inventoryPopup" type="center" :safe-area="false" @close="closeInventoryDialog">
      <view class="inventory-popup-container">
        <!-- 对话框头部 -->
        <view class="popup-header">
          <text class="popup-title">{{ inventoryDialogData.title }}</text>
          <view class="popup-close" @click="closeInventoryDialog">
            <text class="close-icon">×</text>
          </view>
        </view>

        <!-- 对话框内容 -->
        <view class="inventory-dialog-content">
          <view v-if="inventoryDialogData.loading" class="dialog-loading">
            <u-loading-icon mode="spinner" size="40"></u-loading-icon>
            <text class="loading-text">加载中...</text>
          </view>
          <view v-else-if="inventoryDialogData.error" class="dialog-error">
            <text class="error-text">{{ inventoryDialogData.error }}</text>
          </view>
          <view v-else-if="inventoryDialogData.data" class="dialog-inventory-list">
            <!-- 调试信息 -->
<!--            <view style="margin-bottom: 20rpx; padding: 10rpx; background-color: #f0f0f0; border-radius: 8rpx;">-->
<!--              <text style="font-size: 24rpx; color: #666;">-->
<!--                调试: 原料仓上板={{ inventoryDialogData.data.rawStock ? inventoryDialogData.data.rawStock.upperBoard : '无数据' }}-->
<!--              </text>-->
<!--            </view>-->
            <view class="dialog-inventory-item">
              <view class="dialog-warehouse-header">
                <text class="dialog-warehouse-name">贴片仓</text>
              </view>
              <view class="dialog-stock-row">
                <text class="dialog-stock-label">上板:</text>
                <text class="dialog-stock-value">{{ inventoryDialogData.data.smdStock.upperBoard }}</text>
              </view>
              <view class="dialog-stock-row">
                <text class="dialog-stock-label">下板:</text>
                <text class="dialog-stock-value">{{ inventoryDialogData.data.smdStock.lowerBoard }}</text>
              </view>
            </view>

            <view class="dialog-inventory-item">
              <view class="dialog-warehouse-header">
                <text class="dialog-warehouse-name">线边仓</text>
              </view>
              <view class="dialog-stock-row">
                <text class="dialog-stock-label">上板:</text>
                <text class="dialog-stock-value">{{ inventoryDialogData.data.lineStock.upperBoard }}</text>
              </view>
              <view class="dialog-stock-row">
                <text class="dialog-stock-label">下板:</text>
                <text class="dialog-stock-value">{{ inventoryDialogData.data.lineStock.lowerBoard }}</text>
              </view>
            </view>

            <view class="dialog-inventory-item">
              <view class="dialog-warehouse-header">
                <text class="dialog-warehouse-name">原料仓</text>
              </view>
              <view class="dialog-stock-row">
                <text class="dialog-stock-label">上板:</text>
                <text class="dialog-stock-value">{{ inventoryDialogData.data.rawStock.upperBoard }}</text>
              </view>
              <view class="dialog-stock-row">
                <text class="dialog-stock-label">下板:</text>
                <text class="dialog-stock-value">{{ inventoryDialogData.data.rawStock.lowerBoard }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 对话框底部 -->
        <view class="popup-footer">
          <view class="popup-button" @click="closeInventoryDialog">
            <text class="button-text">确定</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { getOrderDetailPage, getUserProfile, showMessage, parseTime, queryAllInventory, updateStepTaskStatus, updateOrderStatus, outboundInventory,inboundSfp,getStepTaskDetail } from '@/api/workOrders.js'
import useUserStore from '@/store/userStore.js'

export default {
  name: 'MyTask',
  data() {
    return {
      // 状态标签页配置
      statusTabs: [
        { label: '未开始', value: 'NEW' },
        { label: '执行中', value: 'IN_PROGRESS' },
        { label: '暂停中', value: 'PAUSED' },
        { label: '已完成', value: 'COMPLETED' }
      ],

      // 当前选中的标签页
      currentTab: 'IN_PROGRESS',

      // 任务列表数据
      taskList: [],

      // 产品展开状态管理
      expandedProducts: {},

      // 分页参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        sortOrder: 'desc',
        orderDispatchStatus: 'DISPATCHED',
        orderStatus: 'IN_PROGRESS',
        assignee: ''
      },

      // 当前用户信息
      currentUser: {
        userId: null,
        nickName: '',
        userName: ''
      },

      // 加载状态
      loading: false,
      loadingMore: false,
      hasMore: true,

      // 总数据量
      total: 0,

      // 库存信息相关
      inventoryData: {}, // 存储每个产品的库存信息 {productKey: {smdStock, lineStock, rawStock}}
      inventoryLoading: {}, // 存储每个产品的库存加载状态 {productKey: boolean}

      // 工序任务状态控制相关
      stepTaskLoading: {}, // 存储每个工序任务的加载状态 {stepTaskId: boolean}

      // 库存对话框相关
      inventoryDialogData: {
        title: '',
        content: '',
        loading: false,
        error: null,
        data: null
      }
    }
  },

  computed: {
    userStore() {
      return useUserStore()
    }
  },

  async onLoad() {
    await this.getCurrentUserInfo()
    await this.getList()
  },



  onReachBottom() {
    this.loadMore()
  },

  methods: {
    /** 获取当前登录用户信息 */
    async getCurrentUserInfo() {
      try {
        // 优先从Pinia store获取用户信息
        if (this.userStore.userInfo && this.userStore.userInfo.userName) {
          const userInfo = this.userStore.userInfo
          this.currentUser = {
            userId: userInfo.id,
            nickName: userInfo.nickName,
            userName: userInfo.userName
          }
          this.queryParams.assignee = userInfo.userName
          return
        }

        // 如果Pinia store没有，显示错误信息
        showMessage('用户信息不存在，请重新登录', 'error')

      } catch (error) {
        showMessage('获取用户信息失败: ' + (error.message || '未知错误'), 'error')
      }
    },

    /** 查询任务列表 */
    async getList() {
      // 检查用户信息
      if (!this.queryParams.assignee) {
        showMessage('用户信息不存在，请重新登录', 'error')
        return
      }

      this.loading = true
      try {
        // console.log('开始API调用，参数:', this.queryParams)
        const response = await getOrderDetailPage(this.queryParams)
        // console.log('API响应:', response)

        // 判断API响应是否成功 (code: 0 或 200 都表示成功)
        if (response && (response.code === 0 || response.code === 200 || response.code === "200")) {
          const { records = [], total = 0 } = response.data || {}
          // console.log('获取到records数量:', records.length, '总数:', total)

          // 处理嵌套数据结构：records[].products[].stepTasks[]
          const processedTasks = this.processTaskData(records)

          if (this.queryParams.pageNum === 1) {
            this.taskList = processedTasks
          } else {
            this.taskList = [...this.taskList, ...processedTasks]
          }

          this.total = total
          this.hasMore = this.taskList.length < total
          // console.log('更新taskList，当前数量:', this.taskList.length, '总数:', this.total)
        } else {
          // console.error('API响应失败，code:', response?.code, 'message:', response?.message)
          showMessage(response?.message || '获取任务列表失败', 'error')
        }
      } catch (error) {
        // console.error('API调用异常:', error)
        showMessage('获取任务列表失败: ' + (error.message || '网络错误'), 'error')
      } finally {
        this.loading = false
        this.loadingMore = false
      }
    },

    /** 处理任务数据结构 - 以工单为单位 */
    processTaskData(records) {
      // console.log('开始处理任务数据，records数量:', records.length)

      const orders = []

      records.forEach((order, orderIndex) => {
        // 检查可能的字段名变化
        const orderId = order.id || order.orderId
        const orderCode = order.orderCode
        const orderType = order.orderType
        const orderStatus = order.orderStatus
        const orderCreatedTime = order.orderCreatedTime || order.createTime || order.createdTime

        // 统计产品信息
        const products = order.products || []
        const productCount = products.length

        // 统计所有步骤任务
        let totalStepTasks = []
        let completedStepTasks = 0

        products.forEach((product) => {
          if (product.stepTasks && product.stepTasks.length > 0) {
            totalStepTasks = [...totalStepTasks, ...product.stepTasks]
          }
        })

        // 统计已完成的步骤任务
        completedStepTasks = totalStepTasks.filter(step => step.isCompleted === 2).length

        const orderData = {
          // 工单基本信息
          orderId: orderId,
          orderCode: orderCode,
          orderType: orderType,
          orderStatus: orderStatus,
          orderCreatedTime: orderCreatedTime,

          // 统计信息
          productCount: productCount,
          totalStepTasks: totalStepTasks.length,
          completedStepTasks: completedStepTasks,

          // 完整的产品和步骤任务数据（用于详情展示）
          products: products,
          allStepTasks: totalStepTasks,

          // 产品预览区域展开状态（默认收起）
          showProductsPreview: false,
          // 产品展开状态
          expandedProducts: {}
        }

        orders.push(orderData)
      })

      // console.log('数据处理完成，最终工单数量:', orders.length)
      return orders
    },

    /** 切换状态标签页 */
    switchTab(status) {
      if (this.currentTab === status) return

      this.currentTab = status
      this.queryParams.orderStatus = status
      this.queryParams.pageNum = 1
      this.taskList = []
      this.hasMore = true

      this.getList()
    },

    /** 刷新数据 */
    onRefresh() {
      this.queryParams.pageNum = 1
      this.taskList = []
      this.hasMore = true
      this.getList()
    },

    /** 上拉加载更多 */
    loadMore() {
      if (this.loadingMore || !this.hasMore || this.loading) return

      this.loadingMore = true
      this.queryParams.pageNum += 1
      this.getList()
    },

    // 工单详情功能已移除

    /** 切换产品预览区域展开/收起 */
    toggleProductsPreview(order) {
      const currentState = order.showProductsPreview || false
      this.$set(order, 'showProductsPreview', !currentState)
      // console.log(`工单 ${order.orderCode} 产品预览区域展开状态:`, !currentState)
    },

    /** 切换产品详情展开/收起 */
    toggleProductDetail(order, productIndex) {
      // 切换展开状态
      if (!order.expandedProducts) {
        this.$set(order, 'expandedProducts', {})
      }

      const currentState = order.expandedProducts[productIndex] || false
      this.$set(order.expandedProducts, productIndex, !currentState)

      // console.log(`产品 ${productIndex} 展开状态:`, !currentState)
    },

    /** 获取状态样式类 */
    getStatusClass(status) {
      const statusMap = {
        'NEW': 'status-new',
        'IN_PROGRESS': 'status-progress',
        'PAUSED': 'status-paused',
        'COMPLETED': 'status-completed'
      }
      return statusMap[status] || 'status-default'
    },

    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        'NEW': '未开始',
        'IN_PROGRESS': '执行中',
        'PAUSED': '暂停中',
        'COMPLETED': '已完成'
      }
      return statusMap[status] || status
    },

    /** 获取工单类型样式类 */
    getOrderTypeClass(orderType) {
      const typeMap = {
        'URGENT': 'type-urgent',
        'NORMAL': 'type-normal'
      }
      return typeMap[orderType] || 'type-normal'
    },

    /** 获取工单类型文本 */
    getOrderTypeText(orderType) {
      const typeMap = {
        'URGENT': '加急',
        'NORMAL': '普通'
      }
      return typeMap[orderType] || '普通'
    },

    /** 获取步骤状态样式类 */
    getStepStatusClass(isCompleted) {
      const statusMap = {
        0: 'step-new',
        1: 'step-progress',
        2: 'step-completed'
      }
      return statusMap[isCompleted] || 'step-default'
    },

    /** 获取步骤状态文本 */
    getStepStatusText(isCompleted) {
      const statusMap = {
        0: '未开始',
        1: '执行中',
        2: '已完成'
      }
      return statusMap[isCompleted] || '未知'
    },

    /** 格式化时间 */
    formatTime(time) {
      if (!time) return '-'
      return parseTime(time, '{y}-{m}-{d} {h}:{i}')
    },

    /** 查看BOM清单 */
    viewBom(product) {
      // 检查必要参数
      if (!product.productName) {
        showMessage('产品名称不能为空', 'error')
        return
      }

      // 跳转到BOM页面，传递产品名称和款式名称
      uni.navigateTo({
        url: `/pages/warehouse/mytask/bom?productName=${encodeURIComponent(product.productName || '')}&styleName=${encodeURIComponent(product.styleName || '')}&styleId=${encodeURIComponent(product.styleId || '')}`
      })
    },

    /** 显示库存信息对话框 */
    async showInventoryDialog(order, productIndex, product) {
      const productKey = this.getProductKey(order, productIndex)

      // 设置对话框标题
      this.inventoryDialogData.title = `${product.productName || '未知产品'} - 库存信息`
      this.inventoryDialogData.content = ''
      this.inventoryDialogData.error = null

      // console.log('准备显示对话框，产品信息:', product)

      // 如果已经有库存数据，直接显示
      if (this.inventoryData[productKey] && !this.inventoryData[productKey].error) {
        this.inventoryDialogData.data = this.inventoryData[productKey]
        this.inventoryDialogData.loading = false
        this.$refs.inventoryPopup.open()
        return
      }

      // 显示对话框并开始加载
      this.inventoryDialogData.loading = true
      this.inventoryDialogData.data = null
      this.$refs.inventoryPopup.open()

      // 直接在这里加载库存数据
      this.loadInventoryData(order, productIndex, product)
    },

    /** 关闭库存信息对话框 */
    closeInventoryDialog() {
      this.$refs.inventoryPopup.close()
      this.inventoryDialogData = {
        title: '',
        content: '',
        loading: false,
        error: null,
        data: null
      }
    },

    /** 加载库存数据 */
    async loadInventoryData(order, productIndex, product) {
      const productKey = this.getProductKey(order, productIndex)

      // 检查必要参数
      if (!product.productName) {
        this.inventoryDialogData.loading = false
        this.inventoryDialogData.error = '产品名称不能为空'
        return
      }

      // 设置加载状态
      this.$set(this.inventoryLoading, productKey, true)

      try {
        // console.log('开始查询库存信息:', {
        //   productName: product.productName,
        //   styleId: product.styleId || 0
        // })

        // 调用库存查询API
        const inventoryResult = await queryAllInventory(
          product.productName,
          product.styleId || 0
        )

        // console.log('库存查询结果:', inventoryResult)

        // 存储库存数据
        this.$set(this.inventoryData, productKey, inventoryResult)

        // 更新对话框数据
        this.inventoryDialogData.loading = false
        this.inventoryDialogData.data = inventoryResult
        this.inventoryDialogData.error = null

        // console.log('对话框数据更新完成:', this.inventoryDialogData)

      } catch (error) {
        // console.error('查询库存信息失败:', error)

        // 设置错误状态
        this.inventoryDialogData.loading = false
        this.inventoryDialogData.error = '查询库存信息失败: ' + (error.message || '未知错误')
        this.inventoryDialogData.data = null

        // 设置空的库存数据以避免重复请求
        this.$set(this.inventoryData, productKey, {
          smdStock: { upperBoard: 0, lowerBoard: 0 },
          lineStock: { upperBoard: 0, lowerBoard: 0 },
          rawStock: { upperBoard: 0, lowerBoard: 0 },
          error: true
        })
      } finally {
        // 清除加载状态
        this.$set(this.inventoryLoading, productKey, false)
      }
    },

    /** 生成产品唯一标识 */
    getProductKey(order, productIndex) {
      return `${order.orderId}-${productIndex}`
    },

    /** 检查库存是否正在加载 */
    isInventoryLoading(order, productIndex) {
      const productKey = this.getProductKey(order, productIndex)
      return this.inventoryLoading[productKey] || false
    },

    /** 检查工序任务是否正在加载 */
    isStepLoading(stepTaskId) {
      return this.stepTaskLoading[stepTaskId] || false
    },

    /** 确认开始工序步骤 */
    confirmStartStep(order, product, step) {
      // 如果正在加载中，不允许操作
      if (this.isStepLoading(step.stepTaskId)) {
        return
      }

      uni.showModal({
        title: '确认操作',
        content: `确定要开始工序步骤"${step.stepName}"吗？`,
        confirmText: '确定',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 用户确认，执行开始操作
            this.handleStartStep(order, product, step)
          }
          // 用户取消，不执行任何操作
        }
      })
    },

    /** 确认完成工序步骤 */
    confirmCompleteStep(order, product, step) {
      // 如果正在加载中，不允许操作
      if (this.isStepLoading(step.stepTaskId)) {
        return
      }

      uni.showModal({
        title: '确认操作',
        content: `确定要完成工序步骤"${step.stepName}"吗？`,
        confirmText: '确定',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 用户确认，执行完成操作
            this.handleCompleteStep(order, product, step)
          }
          // 用户取消，不执行任何操作
        }
      })
    },

    /** 处理"开始"按钮点击 */
    async handleStartStep(order, product, step) {
      try {
        // 设置加载状态
        this.$set(this.stepTaskLoading, step.stepTaskId, true)

        // console.log('开始工序，参数:', {
        //   stepTaskId: step.stepTaskId,
        //   stepName: step.stepName,
        //   currentStatus: step.isCompleted
        // })

        // 调用更新工序任务状态API
        const response = await updateStepTaskStatus(step.stepTaskId, 1)
        // console.log('更新工序任务状态API响应:', response)

        // 根据WMS API规范，成功响应的code应该是0
        if (response && response.code === 0) {
          // 更新本地数据
          step.isCompleted = 1

          // 如果当前工单状态为"未开始"，同时更新工单状态为"执行中"
          if (order.orderStatus === 'NEW') {
            try {
              // console.log('工单状态为NEW，准备更新工单状态为IN_PROGRESS')
              const orderResponse = await updateOrderStatus(order.orderId, 'IN_PROGRESS')
              // console.log('更新工单状态API响应:', orderResponse)

              if (orderResponse && orderResponse.code === 0) {
                order.orderStatus = 'IN_PROGRESS'
                showMessage('工序已开始，工单状态已更新', 'success')
              } else {
                showMessage('工序已开始，但工单状态更新失败', 'warning')
              }
            } catch (orderError) {
              // console.error('更新工单状态失败:', orderError)
              showMessage('工序已开始，但工单状态更新失败', 'warning')
            }
          } else {
            showMessage('工序已开始', 'success')
          }

          // 局部更新工单统计信息，不刷新整个页面
          this.updateOrderStatistics(order)
        } else {
          showMessage(response?.message || '开始工序失败', 'error')
        }
      } catch (error) {
        // console.error('开始工序失败:', error)
        showMessage('开始工序失败: ' + (error.message || '网络错误'), 'error')
      } finally {
        // 清除加载状态
        this.$set(this.stepTaskLoading, step.stepTaskId, false)
      }
    },

    /** 处理"完成"按钮点击 */
    async handleCompleteStep(order, product, step) {
      try {
        // 设置加载状态
        this.$set(this.stepTaskLoading, step.stepTaskId, true)

        // console.log('完成工序，参数:', {
        //   stepTaskId: step.stepTaskId,
        //   stepName: step.stepName,
        //   currentStatus: step.isCompleted
        // })

        // 调用更新工序任务状态API
        const response = await updateStepTaskStatus(step.stepTaskId, 2)
        // console.log('更新工序任务状态API响应:', response)

        // 根据WMS API规范，成功响应的code应该是0
        if (response && response.code === 0) {
          // 更新本地数据
          step.isCompleted = 2
          step.completedAt = new Date().toISOString()

          showMessage('工序已完成', 'success')

          // 局部更新工单统计信息，不刷新整个页面
          this.updateOrderStatistics(order)
        } else {
          showMessage(response?.message || '完成工序失败', 'error')
        }
      } catch (error) {
        // console.error('完成工序失败:', error)
        showMessage('完成工序失败: ' + (error.message || '网络错误'), 'error')
      } finally {
        // 清除加载状态
        this.$set(this.stepTaskLoading, step.stepTaskId, false)
      }
    },

    /** 更新工单统计信息（局部更新，不刷新整个页面） */
    updateOrderStatistics(order) {
      try {
        // 重新计算工单的统计信息
        let totalStepTasks = 0
        let completedStepTasks = 0

        // 遍历所有产品的工序任务
        if (order.products && order.products.length > 0) {
          order.products.forEach(product => {
            if (product.stepTasks && product.stepTasks.length > 0) {
              totalStepTasks += product.stepTasks.length
              completedStepTasks += product.stepTasks.filter(step => step.isCompleted === 2).length
            }
          })
        }

        // 更新工单的统计信息
        order.totalStepTasks = totalStepTasks
        order.completedStepTasks = completedStepTasks

        // 更新 allStepTasks 数组（用于其他地方可能的引用）
        let allStepTasks = []
        if (order.products && order.products.length > 0) {
          order.products.forEach(product => {
            if (product.stepTasks && product.stepTasks.length > 0) {
              allStepTasks = [...allStepTasks, ...product.stepTasks]
            }
          })
        }
        order.allStepTasks = allStepTasks

        // console.log('工单统计信息已更新:', {
        //   orderId: order.orderId,
        //   orderCode: order.orderCode,
        //   totalStepTasks: totalStepTasks,
        //   completedStepTasks: completedStepTasks,
        //   orderStatus: order.orderStatus
        // })

      } catch (error) {
        // console.error('更新工单统计信息失败:', error)
        // 统计信息更新失败不影响主要功能，只记录错误
      }
    },

    /** 处理"提交缺陷"按钮点击 */
    handleSubmitDefect(order, product, step) {
      // 检查stepId是否存在
      if (!step.stepId) {
        showMessage('工序ID不存在', 'error')
        return
      }

      // 使用uni.navigateTo()跳转到defect.vue页面，只传递stepId参数
      uni.navigateTo({
        url: `/pages/warehouse/mytask/defect?stepId=${step.stepId}&stepTaskId=${step.stepTaskId}`
      })
    },

    /** 处理"测试员领取焊接半成品"的领料按钮点击 */
    handleSemifinshedProductThree(order, product, step){
      // 跳转到scanOutbound页面，传递出库相关参数
      const params = {
        orderCode: order.orderCode,
        stepTaskId: step.stepTaskId,
        quantity: product.orderItemQuantity,
        boardType: product.boardType,
        productName: product.productName
      }

      const queryString = Object.keys(params)
          .map(key => `${key}=${params[key]}`)
          .join('&')

      uni.navigateTo({
        url: `/pages/warehouse/mytask/scanOutBound?${queryString}`
      })
    },

    /** 处理"产品入库"的入库按钮点击 */
    handleStorageScanThree(order, product, step){
      const params = {
        orderCode: order.orderCode,
        stepTaskId: step.stepTaskId,
        quantity: product.orderItemQuantity,
        boardType: product.boardType,
        productName: product.productName
      }

      const queryString = Object.keys(params)
          .map(key => `${key}=${params[key]}`)
          .join('&')

      uni.navigateTo({
        url: `/pages/warehouse/mytask/productScanCodeInBound?${queryString}`
      })
    },
    /** 处理"清点上下板数量入线边仓"的入库按钮点击 */
    handleStorageScanTwo(order, product, step) {
      // 检查必要参数
      if (!order.orderCode) {
        showMessage('工单编号不能为空', 'error')
        return
      }
      if (!product.productName) {
        showMessage('产品名称不能为空', 'error')
        return
      }

      if (!step.stepTaskId) {
        showMessage('工序任务ID不存在', 'error')
        return
      }

      if (!product.orderItemQuantity) {
        showMessage('产品数量不能为空', 'error')
        return
      }

      if (!product.boardType) {
        showMessage('产品板型不能为空', 'error')
      }

      // 跳转到scanCode页面，传递入库相关参数
      const params = {
        orderCode: order.orderCode,
        stepTaskId: step.stepTaskId,
        quantity: product.orderItemQuantity,
        boardType: product.boardType,
        productName: product.productName,
        styleId: product.styleId,
        styleName: product.styleName,
        productNumber: product.productId
      }

      const queryString = Object.keys(params)
          .map(key => `${key}=${params[key]}`)
          .join('&')

      uni.navigateTo({
        url: `/pages/warehouse/mytask/scanCode?${queryString}`
      })
    },


    /** 处理"申领半成品PCB板及零件"的领料按钮点击 */
    handleSemifinshedMaterialPicking(order, product, step) {
      // 检查必要参数
      if (!product.productName) {
        showMessage('产品名称不能为空', 'error')
        return
      }

      if (!step.stepTaskId) {
        showMessage('工序任务ID不存在', 'error')
        return
      }

      if (!product.orderItemQuantity) {
        showMessage('产品数量不能为空', 'error')
        return
      }

      if (!product.boardType) {
        showMessage('产品板型不能为空', 'error')
        return
      }

      // 跳转到BOM页面，传递领料相关参数
      const params = {
        productName: encodeURIComponent(product.productName || ''),
        styleName: encodeURIComponent(product.styleName || ''),
        stepTaskId: step.stepTaskId,
        quantity: product.orderItemQuantity,
        boardType: product.boardType,
        styleId: product.styleId,
        source: 'material_picking' // 标识来源为领料
      }

      const queryString = Object.keys(params)
        .map(key => `${key}=${params[key]}`)
        .join('&')

      uni.navigateTo({
        url: `/pages/warehouse/mytask/bom?${queryString}`
      })
    },

    /** 处理"领料"按钮点击 - 基于产品板型选择领料方式，获取已领取数量 */
    async handleMaterialPicking(order, product, step) {
      try {
        console.log('处理领料按钮点击，产品板型:', product.boardType)

        // 检查必要参数
        if (!product.boardType) {
          showMessage('产品板型不能为空', 'error')
          return
        }

        // 显示加载提示
        uni.showLoading({
          title: '获取领取信息...'
        })

        // 先调用 getStepTaskDetail API 获取已领取数量信息
        console.log('开始获取工序任务详细信息，stepTaskId:', step.stepTaskId)
        const response = await getStepTaskDetail(step.stepTaskId)
        console.log('工序任务详细信息API响应:', response)

        uni.hideLoading()

        let onBoardCount = 0
        let downBoardCount = 0
        let oneBoardCount = 0

        // 根据WMS API规范，成功响应的code应该是0
        if (response && response.code === 0) {
          const stepTaskDetail = response.data
          onBoardCount = stepTaskDetail?.onBoard || 0
          downBoardCount = stepTaskDetail?.downBoard || 0
          oneBoardCount = stepTaskDetail?.oneBoard || 0

          console.log('获取到的已领取数量:', {
            onBoard: onBoardCount,
            downBoard: downBoardCount,
            oneBoard: oneBoardCount
          })
        } else {
          console.warn('获取工序任务详细信息失败或响应异常:', response)
          // 即使获取失败，也继续显示选择对话框，但不显示已领取数量
        }

        // 构建板型选择选项，包含已领取数量信息
        this.buildBoardTypeOptionsWithQuantity(order, product, step, onBoardCount, downBoardCount, oneBoardCount)

      } catch (error) {
        uni.hideLoading()
        console.error('获取工序任务详细信息失败:', error)

        // 即使API调用失败，也继续显示选择对话框，但不显示已领取数量
        this.buildBoardTypeOptionsWithQuantity(order, product, step, 0, 0, 0)
      }
    },

    /** 构建包含已领取数量信息的板型选择选项 */
    buildBoardTypeOptionsWithQuantity(order, product, step, onBoardCount, downBoardCount, oneBoardCount) {
      let itemList = []
      let boardTypeMapping = [] // 用于映射选项索引到板型

      const targetQuantity = parseInt(product.orderItemQuantity) || 0

      // 根据产品板型决定显示的选项
      if (product.boardType === '单板') {
        // 单板产品只显示单板选项
        const displayText = oneBoardCount > 0
          ? `单板领料 (已领取: ${oneBoardCount})`
          : '单板领料'

        // 检查是否已完成
        if (oneBoardCount >= targetQuantity) {
          itemList.push(`${displayText} - 已完成`)
        } else {
          itemList.push(displayText)
        }
        boardTypeMapping.push('单板')

      } else if (product.boardType === '上下板') {
        // 上下板产品显示上板和下板选项

        // 上板选项
        const upperDisplayText = onBoardCount > 0
          ? `上板领料 (已领取: ${onBoardCount})`
          : '上板领料'

        if (onBoardCount >= targetQuantity) {
          itemList.push(`${upperDisplayText} - 已完成`)
        } else {
          itemList.push(upperDisplayText)
        }
        boardTypeMapping.push('上板')

        // 下板选项
        const lowerDisplayText = downBoardCount > 0
          ? `下板领料 (已领取: ${downBoardCount})`
          : '下板领料'

        if (downBoardCount >= targetQuantity) {
          itemList.push(`${lowerDisplayText} - 已完成`)
        } else {
          itemList.push(lowerDisplayText)
        }
        boardTypeMapping.push('下板')

      } else {
        // 如果板型不明确，显示所有选项

        // 上板选项
        const upperDisplayText = onBoardCount > 0
          ? `上板领料 (已领取: ${onBoardCount})`
          : '上板领料'

        if (onBoardCount >= targetQuantity) {
          itemList.push(`${upperDisplayText} - 已完成`)
        } else {
          itemList.push(upperDisplayText)
        }
        boardTypeMapping.push('上板')

        // 下板选项
        const lowerDisplayText = downBoardCount > 0
          ? `下板领料 (已领取: ${downBoardCount})`
          : '下板领料'

        if (downBoardCount >= targetQuantity) {
          itemList.push(`${lowerDisplayText} - 已完成`)
        } else {
          itemList.push(lowerDisplayText)
        }
        boardTypeMapping.push('下板')

        // 单板选项
        const singleDisplayText = oneBoardCount > 0
          ? `单板领料 (已领取: ${oneBoardCount})`
          : '单板领料'

        if (oneBoardCount >= targetQuantity) {
          itemList.push(`${singleDisplayText} - 已完成`)
        } else {
          itemList.push(singleDisplayText)
        }
        boardTypeMapping.push('单板')
      }

      console.log('显示板型选择对话框:', {
        boardType: product.boardType,
        targetQuantity: targetQuantity,
        itemList: itemList,
        boardTypeMapping: boardTypeMapping,
        quantities: { onBoardCount, downBoardCount, oneBoardCount }
      })

      // 检查是否所有相关板型都已完成
      const isAllCompleted = this.checkIfAllCompleted(product.boardType, onBoardCount, downBoardCount, oneBoardCount, targetQuantity)

      if (isAllCompleted) {
        uni.showModal({
          title: '领取状态',
          content: `该产品已领料完成！\n\n订单数量：${targetQuantity}\n已领取情况：\n${this.buildCompletionSummary(onBoardCount, downBoardCount, oneBoardCount)}`,
          showCancel: false,
          confirmText: '确定'
        })
        return
      }

      // 显示板型选择对话框
      uni.showActionSheet({
        itemList: itemList,
        success: (res) => {
          const selectedBoardType = boardTypeMapping[res.tapIndex]
          console.log('用户选择的板型:', selectedBoardType)

          // 选择完成后进入扫码流程
          this.startScanCode(order, product, step, selectedBoardType, 0, null)
        },
        fail: (err) => {
          console.log('用户取消选择板型')
        }
      })
    },

    /** 检查是否所有相关板型都已完成 */
    checkIfAllCompleted(boardType, onBoardCount, downBoardCount, oneBoardCount, targetQuantity) {
      if (boardType === '单板') {
        // 单板产品：单板数量达到目标数量即完成
        return oneBoardCount >= targetQuantity
      } else if (boardType === '上下板') {
        // 上下板产品：上板和下板数量都达到目标数量才完成
        return onBoardCount >= targetQuantity && downBoardCount >= targetQuantity
      } else {
        // 板型不明确：任意一种方式完成即可
        const isSingleCompleted = oneBoardCount >= targetQuantity
        const isUpperLowerCompleted = onBoardCount >= targetQuantity && downBoardCount >= targetQuantity
        return isSingleCompleted || isUpperLowerCompleted
      }
    },

    /** 构建完成情况摘要 */
    buildCompletionSummary(onBoardCount, downBoardCount, oneBoardCount) {
      const summary = []

      if (onBoardCount > 0) {
        summary.push(`上板：${onBoardCount}`)
      }
      if (downBoardCount > 0) {
        summary.push(`下板：${downBoardCount}`)
      }
      if (oneBoardCount > 0) {
        summary.push(`单板：${oneBoardCount}`)
      }

      return summary.length > 0 ? summary.join('\n') : '暂无领取记录'
    },

    /** 构建可用的入库板型选择选项，实现互斥逻辑和隐藏逻辑 */
    buildAvailableStorageBoardOptions(onBoardCount, downBoardCount, oneBoardCount, orderQuantity) {
      const options = []

      // 检查是否有任何已入库数量
      const hasOnBoard = onBoardCount > 0
      const hasDownBoard = downBoardCount > 0
      const hasOneBoard = oneBoardCount > 0

      // 互斥逻辑：
      // 1. 如果单板有已入库数量，则隐藏上板和下板选项
      // 2. 如果上板或下板有已入库数量，则隐藏单板选项，但上板和下板可以同时存在
      // 3. 上板和下板是同时存在的，可以分别选择入库

      if (hasOneBoard) {
        // 如果单板有已入库数量，只显示单板选项
        if (oneBoardCount < orderQuantity) {
          const remainingQuantity = orderQuantity - oneBoardCount
          options.push({
            boardType: '单板',
            currentCount: oneBoardCount,
            displayText: `单板入库 (已入库: ${oneBoardCount}, 还需: ${remainingQuantity})`
          })
        }
      } else if (hasOnBoard || hasDownBoard) {
        // 如果上板或下板有已入库数量，隐藏单板选项，但显示上板和下板选项
        // 检查上板和下板的完成状态
        const isOnBoardCompleted = onBoardCount >= orderQuantity
        const isDownBoardCompleted = downBoardCount >= orderQuantity

        // 只要上板或下板任意一个未完成，就同时显示两个选项
        if (!isOnBoardCompleted || !isDownBoardCompleted) {
          // 上板选项：始终显示（无论是否完成）
          if (isOnBoardCompleted) {
            options.push({
              boardType: '上板',
              currentCount: onBoardCount,
              displayText: `上板入库 (已入库: ${onBoardCount} - 已完成)`
            })
          } else {
            const remainingQuantity = orderQuantity - onBoardCount
            const displayText = hasOnBoard
              ? `上板入库 (已入库: ${onBoardCount}, 还需: ${remainingQuantity})`
              : `上板入库`
            options.push({
              boardType: '上板',
              currentCount: onBoardCount,
              displayText: displayText
            })
          }

          // 下板选项：始终显示（无论是否完成）
          if (isDownBoardCompleted) {
            options.push({
              boardType: '下板',
              currentCount: downBoardCount,
              displayText: `下板入库 (已入库: ${downBoardCount} - 已完成)`
            })
          } else {
            const remainingQuantity = orderQuantity - downBoardCount
            const displayText = hasDownBoard
              ? `下板入库 (已入库: ${downBoardCount}, 还需: ${remainingQuantity})`
              : `下板入库`
            options.push({
              boardType: '下板',
              currentCount: downBoardCount,
              displayText: displayText
            })
          }
        }
      } else {
        // 如果没有任何已入库数量，显示所有选项（但不显示已入库数量信息）
        options.push({
          boardType: '上板',
          currentCount: onBoardCount,
          displayText: `上板入库`
        })
        options.push({
          boardType: '下板',
          currentCount: downBoardCount,
          displayText: `下板入库`
        })
        options.push({
          boardType: '单板',
          currentCount: oneBoardCount,
          displayText: `单板入库`
        })
      }

      return options
    },

    /** 开始扫码流程 */
    startScanCode(order, product, step, boardType, currentCount = 0, stepTaskDetail = null) {
      // 调用uni-app扫码API
      uni.scanCode({
        scanType: ['qrCode', 'barCode'], // 支持二维码和条形码
        success: (res) => {
          // 扫码成功，处理扫码结果
          this.processScanResult(res.result, order, product, step, boardType, currentCount, stepTaskDetail)
        },
        fail: (err) => {
          // 扫码失败处理
          this.handleScanError(err)
        }
      })
    },

    /** 处理扫码结果 */
    processScanResult(scanData, order, product, step, boardType, currentCount = 0, stepTaskDetail = null) {
      try {
        const orderQuantity = product.orderItemQuantity || 0

        // 构建确认内容，只显示基本的领取信息
        let content = `领取${product.productName}的${boardType}原料，数量${orderQuantity}`

        // 显示扫码结果弹窗 - 只显示基本的领取信息
        uni.showModal({
          title: '扫码结果确认',
          content: content,
          confirmText: '确定',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 解析扫码数据，提取purchase_no并执行出库操作
              this.handleOutboundInventory(scanData, product, boardType, step).then(() => {
                // 出库成功后，执行与"开始"按钮相同的业务逻辑
                this.handleStartStep(order, product, step)
              }).catch((error) => {
                console.error('出库操作失败，不执行开始工序:', error)
              })
            }
          }
        })
      } catch (error) {
        console.error('处理扫码结果失败:', error)
        showMessage('处理扫码结果失败: ' + (error.message || '未知错误'), 'error')
      }
    },

    /** 处理出库操作 */
    async handleOutboundInventory(scanData, product, boardType, step) {
      try {
        console.log('开始处理出库操作')
        console.log('扫码数据:', scanData)
        console.log('产品信息:', product)
        console.log('板型:', boardType)
        console.log('工序任务信息:', {
          stepTaskId: step?.stepTaskId,
          stepName: step?.stepName
        })

        // 解析扫码数据，提取purchase_no
        const purchaseNo = this.extractPurchaseNo(scanData)
        console.log('解析出的purchaseNo:', purchaseNo)

        if (!purchaseNo) {
          console.error('无法解析purchase_no，扫码数据:', scanData)
          uni.showToast({
            title: `扫码数据格式错误，无法解析采购单号。扫码内容：${scanData}`,
            icon: 'none',
            duration: 5000
          })
          throw new Error('无法解析采购单号')
        }

        // 验证必需参数
        if (!product.orderItemQuantity || !product.productName || !step?.stepTaskId) {
          console.error('参数信息不完整:', {
            orderItemQuantity: product.orderItemQuantity,
            productName: product.productName,
            stepTaskId: step?.stepTaskId
          })

          const missingFields = []
          if (!product.orderItemQuantity) missingFields.push('数量')
          if (!product.productName) missingFields.push('产品名称')
          if (!step?.stepTaskId) missingFields.push('工序任务ID')

          uni.showToast({
            title: `参数信息不完整，缺少：${missingFields.join('、')}`,
            icon: 'none',
            duration: 4000
          })
          throw new Error('参数信息不完整')
        }

        // 准备出库API参数
        const formData = {
          quantity: product.orderItemQuantity,
          purchaseNo: purchaseNo,
          itemName: product.productName,
          boardType: boardType,
          stepTaskId: step.stepTaskId
        }

        console.log('出库API调用参数:', formData)

        // 调用出库API
        const response = await outboundInventory(formData)

        console.log('出库API响应:', response)
        console.log('响应码:', response.code)
        console.log('响应消息:', response.message)

        if (response.code === 0 || response.code === 200) {
          uni.showToast({
            title: '出库成功',
            icon: 'success',
            duration: 2000
          })
          // 出库成功，正常返回
          return
        } else {
          console.error('出库失败详情:', {
            code: response.code,
            message: response.message,
            data: response.data
          })

          // 显示后端返回的具体错误原因
          const errorMessage = response.message || response.msg || '出库操作失败，请稍后重试'
          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 5000
          })
          throw new Error(errorMessage)
        }
      } catch (error) {
        console.error('出库操作异常:', error)
        console.error('错误详情:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        })

        // 处理不同类型的错误
        let errorMessage = '网络请求失败，请检查网络连接'

        if (error.message) {
          if (error.message.includes('timeout')) {
            errorMessage = '请求超时，请稍后重试'
          } else if (error.message.includes('Network')) {
            errorMessage = '网络连接失败，请检查网络'
          } else {
            errorMessage = error.message
          }
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 5000
        })
        throw error
      }
    },

    /** 从扫码数据中提取purchase_no */
    extractPurchaseNo(scanData) {
      try {
        // 扫码数据格式示例：label_type:purchase|purchase_no:PO20250724001
        if (!scanData || typeof scanData !== 'string') {
          return null
        }

        // 使用正则表达式提取purchase_no的值
        const purchaseNoMatch = scanData.match(/purchase_no:([^|]+)/i)

        if (purchaseNoMatch && purchaseNoMatch[1]) {
          const purchaseNo = purchaseNoMatch[1].trim()
          console.log('解析出的purchase_no:', purchaseNo)
          return purchaseNo
        }

        // 如果正则匹配失败，尝试手动解析
        const parts = scanData.split('|')
        for (const part of parts) {
          if (part.toLowerCase().includes('purchase_no:')) {
            const value = part.split(':')[1]
            if (value) {
              const purchaseNo = value.trim()
              console.log('手动解析出的purchase_no:', purchaseNo)
              return purchaseNo
            }
          }
        }

        return null
      } catch (error) {
        console.error('解析purchase_no失败:', error)
        return null
      }
    },

    /** 处理扫码错误 */
    handleScanError(error) {
      console.error('扫码失败:', error)

      // 根据错误类型显示不同的提示信息
      if (error.errMsg && error.errMsg.includes('cancel')) {
        // 用户取消扫码
        showMessage('已取消扫码', 'info')
      } else if (error.errMsg && error.errMsg.includes('fail')) {
        // 扫码失败
        showMessage('扫码失败，请重试', 'error')
      } else {
        // 其他错误
        showMessage('扫码出现错误: ' + (error.errMsg || '未知错误'), 'error')
      }
    },

    /** 处理"入库"按钮点击 - 基于产品板型选择入库方式，获取已入库数量 */
    async handleStorageScan(order, product, step) {
      try {
        console.log('处理入库按钮点击，产品板型:', product.boardType)

        // 检查必要参数
        if (!product.boardType) {
          showMessage('产品板型不能为空', 'error')
          return
        }

        // 显示加载提示
        uni.showLoading({
          title: '获取入库信息...'
        })

        // 先调用 getStepTaskDetail API 获取已入库数量信息
        console.log('开始获取工序任务详细信息，stepTaskId:', step.stepTaskId)
        const response = await getStepTaskDetail(step.stepTaskId)
        console.log('工序任务详细信息API响应:', response)

        uni.hideLoading()

        let onBoardCount = 0
        let downBoardCount = 0
        let oneBoardCount = 0

        // 根据WMS API规范，成功响应的code应该是0
        if (response && response.code === 0) {
          const stepTaskDetail = response.data
          onBoardCount = stepTaskDetail?.onBoard || 0
          downBoardCount = stepTaskDetail?.downBoard || 0
          oneBoardCount = stepTaskDetail?.oneBoard || 0

          console.log('获取到的已入库数量:', {
            onBoard: onBoardCount,
            downBoard: downBoardCount,
            oneBoard: oneBoardCount
          })
        } else {
          console.warn('获取工序任务详细信息失败或响应异常:', response)
          // 即使获取失败，也继续显示选择对话框，但不显示已入库数量
        }

        // 构建板型选择选项，包含已入库数量信息
        this.buildStorageBoardTypeOptionsWithQuantity(order, product, step, onBoardCount, downBoardCount, oneBoardCount)

      } catch (error) {
        uni.hideLoading()
        console.error('获取工序任务详细信息失败:', error)

        // 即使API调用失败，也继续显示选择对话框，但不显示已入库数量
        this.buildStorageBoardTypeOptionsWithQuantity(order, product, step, 0, 0, 0)
      }
    },


    /** 构建包含已入库数量信息的板型选择选项 */
    buildStorageBoardTypeOptionsWithQuantity(order, product, step, onBoardCount, downBoardCount, oneBoardCount) {
      let itemList = []
      let boardTypeMapping = [] // 用于映射选项索引到板型

      const targetQuantity = parseInt(product.orderItemQuantity) || 0

      // 根据产品板型决定显示的选项
      if (product.boardType === '单板') {
        // 单板产品只显示单板选项
        const displayText = oneBoardCount > 0
          ? `单板入库 (已入库: ${oneBoardCount})`
          : '单板入库'

        // 检查是否已完成
        if (oneBoardCount >= targetQuantity) {
          itemList.push(`${displayText} - 已完成`)
        } else {
          itemList.push(displayText)
        }
        boardTypeMapping.push('单板')

      } else if (product.boardType === '上下板') {
        // 上下板产品显示上板和下板选项

        // 上板选项
        const upperDisplayText = onBoardCount > 0
          ? `上板入库 (已入库: ${onBoardCount})`
          : '上板入库'

        if (onBoardCount >= targetQuantity) {
          itemList.push(`${upperDisplayText} - 已完成`)
        } else {
          itemList.push(upperDisplayText)
        }
        boardTypeMapping.push('上板')

        // 下板选项
        const lowerDisplayText = downBoardCount > 0
          ? `下板入库 (已入库: ${downBoardCount})`
          : '下板入库'

        if (downBoardCount >= targetQuantity) {
          itemList.push(`${lowerDisplayText} - 已完成`)
        } else {
          itemList.push(lowerDisplayText)
        }
        boardTypeMapping.push('下板')

      } else {
        // 如果板型不明确，显示所有选项

        // 上板选项
        const upperDisplayText = onBoardCount > 0
          ? `上板入库 (已入库: ${onBoardCount})`
          : '上板入库'

        if (onBoardCount >= targetQuantity) {
          itemList.push(`${upperDisplayText} - 已完成`)
        } else {
          itemList.push(upperDisplayText)
        }
        boardTypeMapping.push('上板')

        // 下板选项
        const lowerDisplayText = downBoardCount > 0
          ? `下板入库 (已入库: ${downBoardCount})`
          : '下板入库'

        if (downBoardCount >= targetQuantity) {
          itemList.push(`${lowerDisplayText} - 已完成`)
        } else {
          itemList.push(lowerDisplayText)
        }
        boardTypeMapping.push('下板')

        // 单板选项
        const singleDisplayText = oneBoardCount > 0
          ? `单板入库 (已入库: ${oneBoardCount})`
          : '单板入库'

        if (oneBoardCount >= targetQuantity) {
          itemList.push(`${singleDisplayText} - 已完成`)
        } else {
          itemList.push(singleDisplayText)
        }
        boardTypeMapping.push('单板')
      }

      console.log('显示入库板型选择对话框:', {
        boardType: product.boardType,
        targetQuantity: targetQuantity,
        itemList: itemList,
        boardTypeMapping: boardTypeMapping,
        quantities: { onBoardCount, downBoardCount, oneBoardCount }
      })

      // 检查是否所有相关板型都已完成
      const isAllCompleted = this.checkIfAllStorageCompleted(product.boardType, onBoardCount, downBoardCount, oneBoardCount, targetQuantity)

      if (isAllCompleted) {
        uni.showModal({
          title: '入库状态',
          content: `该产品已入库完成！\n\n订单数量：${targetQuantity}\n已入库情况：\n${this.buildCompletionSummary(onBoardCount, downBoardCount, oneBoardCount)}`,
          showCancel: false,
          confirmText: '确定'
        })
        return
      }

      // 显示板型选择对话框
      uni.showActionSheet({
        itemList: itemList,
        success: (res) => {
          const selectedBoardType = boardTypeMapping[res.tapIndex]
          console.log('用户选择的板型:', selectedBoardType)

          // 选择完成后进入扫码流程
          this.startStorageScanCode(order, product, step, selectedBoardType, 0, null)
        },
        fail: (err) => {
          console.log('用户取消选择板型')
        }
      })
    },

    /** 检查是否所有相关板型都已完成入库 */
    checkIfAllStorageCompleted(boardType, onBoardCount, downBoardCount, oneBoardCount, targetQuantity) {
      if (boardType === '单板') {
        // 单板产品：单板数量达到目标数量即完成
        return oneBoardCount >= targetQuantity
      } else if (boardType === '上下板') {
        // 上下板产品：上板和下板数量都达到目标数量才完成
        return onBoardCount >= targetQuantity && downBoardCount >= targetQuantity
      } else {
        // 板型不明确：任意一种方式完成即可
        const isSingleCompleted = oneBoardCount >= targetQuantity
        const isUpperLowerCompleted = onBoardCount >= targetQuantity && downBoardCount >= targetQuantity
        return isSingleCompleted || isUpperLowerCompleted
      }
    },

    /** 开始入库扫码流程 */
    startStorageScanCode(order, product, step, boardType, currentCount = 0, stepTaskDetail = null) {
      // 调用uni-app扫码API
      uni.scanCode({
        scanType: ['qrCode', 'barCode'], // 支持二维码和条形码
        success: (res) => {
          // 扫码成功，处理扫码结果
          this.processStorageScanResult(res.result, order, product, step, boardType, currentCount, stepTaskDetail)
        },
        fail: (err) => {
          // 扫码失败处理
          this.handleStorageScanError(err)
        }
      })
    },

    /** 处理入库扫码结果 */
    processStorageScanResult(scanData, order, product, step, boardType, currentCount = 0, stepTaskDetail = null) {
      try {
        const orderQuantity = product.orderItemQuantity || 0

        // 解析扫码数据，提取zone_code
        const zoneCode = this.extractZoneCode(scanData)
        console.log('解析出的zone_code:', zoneCode)

        if (!zoneCode) {
          uni.showModal({
            title: '扫码数据错误',
            content: `无法解析仓库区域代码，请确认二维码格式正确。\n\n扫码内容：${scanData}`,
            showCancel: false,
            confirmText: '确定'
          })
          return
        }

        // 构建确认内容，只显示基本的入库信息
        let content = `入库${product.productName}的${boardType}，数量${orderQuantity}，仓库区域：${zoneCode}`

        // 显示扫码结果弹窗 - 只显示基本的入库信息
        uni.showModal({
          title: '扫码结果确认',
          content: content,
          confirmText: '确定',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 执行入库操作，传递完整的参数
              this.executeStorageInboundOperation(scanData, order, product, boardType, step, zoneCode).then(() => {
                // 入库成功后，如果工序状态为"未开始"，则执行工序开始逻辑
                if (step.isCompleted === 0) {
                  console.log('入库成功，工序状态为未开始，执行工序开始逻辑')
                  this.handleStartStep(order, product, step)
                } else {
                  console.log('入库成功，工序已开始，无需重复开始工序，当前状态:', step.isCompleted)
                }
              }).catch((error) => {
                console.error('入库操作失败，不执行开始工序:', error)
              })
            }
          }
        })
      } catch (error) {
        console.error('处理入库扫码结果失败:', error)
        showMessage('处理扫码结果失败: ' + (error.message || '未知错误'), 'error')
      }
    },

    /** 处理入库操作 */
    async executeStorageInboundOperation(scanData, order, product, boardType, step, zoneCode) {
      try {
        console.log('开始处理入库操作')
        console.log('扫码数据:', scanData)
        console.log('工单信息:', order)
        console.log('产品信息:', product)
        console.log('板型:', boardType)
        console.log('工序任务信息:', {
          stepTaskId: step?.stepTaskId,
          stepName: step?.stepName
        })
        console.log('仓库区域代码:', zoneCode)

        // 验证必需参数
        if (!order.orderCode || !product.orderItemQuantity || !product.productName || !product.productId || !step?.stepTaskId || !step?.stepName) {
          console.error('参数信息不完整:', {
            orderCode: order.orderCode,
            orderItemQuantity: product.orderItemQuantity,
            productName: product.productName,
            productId: product.productId,
            stepTaskId: step?.stepTaskId,
            stepName: step?.stepName
          })

          const missingFields = []
          if (!order.orderCode) missingFields.push('工单编号')
          if (!product.orderItemQuantity) missingFields.push('数量')
          if (!product.productName) missingFields.push('产品名称')
          if (!product.productId) missingFields.push('产品编号')
          if (!step?.stepTaskId) missingFields.push('工序任务ID')
          if (!step?.stepName) missingFields.push('工序名称')

          uni.showToast({
            title: `参数信息不完整，缺少：${missingFields.join('、')}`,
            icon: 'none',
            duration: 4000
          })
          throw new Error('参数信息不完整')
        }

        // 准备入库API参数 - 使用完整的参数结构
        const apiParams = {
          orderCode: order.orderCode,           // 工单编号
          quantity: product.orderItemQuantity,  // 产品数量
          productNumber: product.productId,     // 产品编号
          itemName: product.productName,        // 产品名称
          boardType: boardType,                 // 板型（上板/下板/单板）
          stepTaskId: step.stepTaskId,          // 工序任务ID
          stepName: step.stepName,              // 工序名称
          zoneCode: zoneCode,                    // 从扫码结果中解析出的仓库区域代码
          styleName: product.styleName           // 样式名称
        }

        console.log('入库API调用参数:', apiParams)

        // 调用入库API
        const response = await inboundSfp(apiParams)

        console.log('入库API响应:', response)
        console.log('响应码:', response.code)
        console.log('响应消息:', response.message)

        if (response.code === 0 || response.code === 200) {
          uni.showToast({
            title: '入库成功',
            icon: 'success',
            duration: 2000
          })
          // 入库成功，正常返回
          return
        } else {
          console.error('入库失败详情:', {
            code: response.code,
            message: response.message,
            data: response.data
          })

          // 显示后端返回的具体错误原因
          const errorMessage = response.message || response.msg || '入库操作失败，请稍后重试'
          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 5000
          })
          throw new Error(errorMessage)
        }
      } catch (error) {
        console.error('入库操作异常:', error)
        console.error('错误详情:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        })

        // 处理不同类型的错误
        let errorMessage = '网络请求失败，请检查网络连接'

        if (error.message) {
          if (error.message.includes('timeout')) {
            errorMessage = '请求超时，请稍后重试'
          } else if (error.message.includes('Network')) {
            errorMessage = '网络连接失败，请检查网络'
          } else {
            errorMessage = error.message
          }
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 5000
        })
        throw error
      }
    },

    /** 处理入库扫码错误 */
    handleStorageScanError(error) {
      console.error('入库扫码失败:', error)

      // 根据错误类型显示不同的提示信息
      if (error.errMsg && error.errMsg.includes('cancel')) {
        // 用户取消扫码
        showMessage('已取消入库扫码', 'info')
      } else if (error.errMsg && error.errMsg.includes('fail')) {
        // 扫码失败
        showMessage('入库扫码失败，请重试', 'error')
      } else {
        // 其他错误
        showMessage('入库扫码出现错误: ' + (error.errMsg || '未知错误'), 'error')
      }
    },

    /** 从入库扫码数据中提取zone_code */
    extractZoneCode(scanData) {
      try {
        // 扫码数据格式示例：label_type:warehouse_zone|zone_code:SFQ20250601
        if (!scanData || typeof scanData !== 'string') {
          console.error('扫码数据为空或格式错误:', scanData)
          return null
        }

        console.log('开始解析zone_code，原始数据:', scanData)

        // 使用正则表达式提取zone_code的值
        const zoneCodeMatch = scanData.match(/zone_code:([^|]+)/i)

        if (zoneCodeMatch && zoneCodeMatch[1]) {
          const zoneCode = zoneCodeMatch[1].trim()
          console.log('正则匹配成功，解析出的zone_code:', zoneCode)
          return zoneCode
        }

        // 如果正则匹配失败，尝试手动解析
        console.log('正则匹配失败，尝试手动解析')
        const parts = scanData.split('|')
        for (const part of parts) {
          const trimmedPart = part.trim()
          if (trimmedPart.toLowerCase().includes('zone_code:')) {
            const colonIndex = trimmedPart.indexOf(':')
            if (colonIndex !== -1 && colonIndex < trimmedPart.length - 1) {
              const zoneCode = trimmedPart.substring(colonIndex + 1).trim()
              console.log('手动解析成功，解析出的zone_code:', zoneCode)
              return zoneCode
            }
          }
        }

        console.error('无法解析zone_code，扫码数据格式不正确:', scanData)
        return null
      } catch (error) {
        console.error('解析zone_code时发生异常:', error)
        return null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.my-order-task {
  background-color: #f5f5f5; /* 设置页面背景色 */
  min-height: 100vh; /* 确保页面高度 */

  /* 固定状态标签页容器 */
  .tab-bar-fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 98; /* 低于nav-bar的z-index: 99 */
    background-color: #f5f5f5;
    /* #ifndef H5 */
    padding-top: calc(var(--status-bar-height) + 100rpx); /* 移除多余的20rpx间距 */
    /* #endif */
    /* #ifdef H5 */
    padding-top: 100rpx; /* H5环境下移除多余间距 */
    /* #endif */
    padding-left: 0rpx;
    padding-right: 0rpx;
    padding-bottom: 0; /* 确保没有底部间距 */

    .tab-bar {
      display: flex;
      background-color: #fff;
      border-bottom: 1rpx solid #e8e8e8;
      border-radius: 10rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      margin-bottom: 0; /* 确保没有额外的下边距 */

      .tab-item {
        flex: 1;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 24rpx 0;

        .tab-text {
          font-size: 28rpx;
          color: #666;
          transition: color 0.3s;
        }

        .tab-indicator {
          position: absolute;
          bottom: 0;
          width: 60rpx;
          height: 4rpx;
          background-color: #007aff;
          border-radius: 2rpx;
        }

        &.active .tab-text {
          color: #007aff;
          font-weight: 600;
        }
      }
    }
  }

  .content {
    padding: 0; /* 完全移除内边距 */
    margin-top: 40rpx;
    /* #ifndef H5 */
   // margin-top: calc(var(--status-bar-height) + 148rpx); /* 精确匹配修正后的导航高度：状态栏 + nav-bar(100rpx) + tab-item(48rpx) */
    /* #endif */
    /* #ifdef H5 */
    //margin-top: 148rpx; /* H5环境下精确匹配：nav-bar(100rpx) + tab-item(48rpx) */
    /* #endif */
    /* 任务列表 */
    .task-list {
      /* #ifndef H5 */
      height: calc(100vh - var(--status-bar-height) - 148rpx); /* 精确匹配修正后的导航高度 */
      /* #endif */
      /* #ifdef H5 */
      height: calc(100vh - 148rpx); /* H5环境下精确匹配修正后的导航高度 */
      /* #endif */
      padding: 0; /* 确保没有内边距 */
      margin: 0; /* 确保没有外边距 */
      box-sizing: border-box;
      background-color: #f5f5f5; /* 设置背景色与页面背景色一致 */
    }

    /* 加载状态 */
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 100rpx 0;

      .loading-text {
        margin-top: 20rpx;
        font-size: 28rpx;
        color: #999;
      }
    }

    /* 空状态 */
    .empty-container {
      display: flex;
      justify-content: center;
      padding: 100rpx 0;
    }

    /* 工单卡片 */
    .order-cards {
    width: 100%;
    box-sizing: border-box;
    padding: 50rpx 20rpx 0 20rpx; /* 恢复顶部间距，确保与状态导航有适当距离 */

    .order-card {
      width: 100%;
      background-color: #fff;
      border-radius: 16rpx;
      margin-bottom: 20rpx;
      padding: 20rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      box-sizing: border-box;

      /* 第一个卡片不需要上边距，因为空间计算已精确匹配 */
      &:first-child {
        margin-top: 0;
      }

    /* 卡片头部 */
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20rpx;
      width: 100%;
      box-sizing: border-box;

      .order-info {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 320rpx; /* 确保工单编号有足够空间 */

        .order-code {
          font-size: 30rpx;
          font-weight: 600;
          color: #333;
          margin-right: 12rpx;
          white-space: nowrap;
          overflow: visible;
          flex-shrink: 0;
          min-width: 280rpx;
        }

        .status-badge {
          padding: 4rpx 10rpx;
          border-radius: 12rpx;
          font-size: 20rpx;
          color: #fff;
          white-space: nowrap;
          flex-shrink: 0;

          &.status-new {
            background-color: #909399;
          }

          &.status-progress {
            background-color: #409eff;
          }

          &.status-paused {
            background-color: #e6a23c;
          }

          &.status-completed {
            background-color: #67c23a;
          }

          &.status-default {
            background-color: #999;
          }
        }
      }

      .order-type-badge {
        padding: 4rpx 10rpx;
        border-radius: 12rpx;
        font-size: 20rpx;
        color: #fff;
        white-space: nowrap;
        flex-shrink: 0;

        &.type-urgent {
          background-color: #f56c6c;
        }

        &.type-normal {
          background-color: #909399;
        }
      }
    }

    /* 卡片内容 */
    .card-content {
      width: 100%;
      box-sizing: border-box;

      .stats-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20rpx;
        width: 100%;
        box-sizing: border-box;

        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          flex: 1;
          min-width: 0; /* 允许内容收缩 */
          padding: 0 4rpx;
          box-sizing: border-box;

          .stat-label {
            font-size: 22rpx;
            color: #666;
            margin-bottom: 6rpx;
            text-align: center;
            white-space: nowrap;
          }

          .stat-value {
            font-size: 26rpx;
            font-weight: 600;
            color: #333;
            text-align: center;

            &.completed {
              color: #67c23a;
            }

            &.progress {
              color: #409eff;
            }
          }
        }
      }

      .progress-bar {
        margin-bottom: 20rpx;

        .progress-track {
          width: 100%;
          height: 8rpx;
          background-color: #f0f0f0;
          border-radius: 4rpx;
          overflow: hidden;

          .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
            border-radius: 4rpx;
            transition: width 0.3s ease;
          }
        }
      }

      /* 产品预览控制按钮 */
      .products-toggle {
        margin-bottom: 16rpx;
        width: 100%;
        box-sizing: border-box;

        .toggle-button {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 14rpx 16rpx;
          background-color: #f8f9fa;
          border-radius: 12rpx;
          border: 2rpx solid #e9ecef;
          transition: all 0.3s ease;
          cursor: pointer;
          width: 100%;
          box-sizing: border-box;

          &:hover {
            background-color: #e9ecef;
            border-color: #007aff;
          }

          .toggle-text {
            font-size: 28rpx;
            font-weight: 600;
            color: #333;
          }

          .toggle-icon {
            width: 40rpx;
            height: 40rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s ease;

            .arrow-down {
              font-size: 20rpx;
              color: #007aff;
            }

            &.rotated {
              transform: rotate(180deg);
            }
          }
        }
      }

      .products-preview {
        .section-title {
          display: block;
          font-size: 28rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 16rpx;
        }

        .product-list {
          width: 100%;
          box-sizing: border-box;

          .product-item {
            padding: 14rpx;
            background-color: #f8f9fa;
            border-radius: 12rpx;
            margin-bottom: 12rpx;
            border: 2rpx solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
            width: 100%;
            box-sizing: border-box;

            &:hover {
              background-color: #e9ecef;
            }

            &.expanded {
              border-color: #007aff;
              background-color: #f0f8ff;
            }

            .product-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              width: 100%;
              box-sizing: border-box;

              .product-info {
                flex: 1;
                min-width: 0; /* 允许内容收缩 */

                .product-name {
                  display: block;
                  font-size: 26rpx;
                  color: #333;
                  font-weight: 600;
                  margin-bottom: 6rpx;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }

                .product-style {
                  display: inline-block;
                  font-size: 22rpx;
                  color: #666;
                  margin-right: 16rpx;
                }

                .product-quantity {
                  display: inline-block;
                  font-size: 22rpx;
                  color: #666;
                }
              }



              .product-actions {
                display: flex;
                align-items: center;
                gap: 12rpx;

                .bom-button {
                  padding: 8rpx 12rpx;
                  transition: opacity 0.2s ease;

                  &:active {
                    opacity: 0.6;
                  }

                  .bom-text {
                    font-size: 24rpx;
                    color: #007aff;
                    font-weight: 500;
                    white-space: nowrap;
                  }
                }

                .expand-icon {
                  width: 40rpx;
                  height: 40rpx;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  transition: transform 0.3s ease;

                  .arrow-down {
                    font-size: 20rpx;
                    color: #007aff;
                  }

                  &.rotated {
                    transform: rotate(180deg);
                  }
                }
              }
            }

            .product-details {
              margin-top: 16rpx;
              padding-top: 16rpx;
              border-top: 1rpx solid #e0e0e0;

              .product-meta {
                margin-bottom: 16rpx;

                .meta-item {
                  display: block;
                  font-size: 22rpx;
                  color: #666;
                  margin-bottom: 6rpx;
                }
              }

              .step-tasks {
                .step-title {
                  display: block;
                  font-size: 26rpx;
                  font-weight: 600;
                  color: #333;
                  margin-bottom: 12rpx;
                }

                .step-list {
                  .step-item {
                    padding: 12rpx;
                    background-color: #fff;
                    border-radius: 8rpx;
                    margin-bottom: 8rpx;
                    border-left: 4rpx solid #ddd;

                    &.step-new {
                      border-left-color: #909399;
                    }

                    &.step-progress {
                      border-left-color: #409eff;
                    }

                    &.step-completed {
                      border-left-color: #67c23a;
                    }

                    .step-header {
                      display: flex;
                      justify-content: space-between;
                      align-items: center;
                      margin-bottom: 8rpx;

                      .step-info {
                        flex: 1;

                        .step-name {
                          display: block;
                          font-size: 24rpx;
                          color: #333;
                          font-weight: 500;
                          margin-bottom: 4rpx;
                        }

                        .step-number {
                          font-size: 20rpx;
                          color: #999;
                        }
                      }

                      .step-right {
                        display: flex;
                        align-items: center;
                        gap: 12rpx;

                        .step-status-badge {
                          padding: 2rpx 8rpx;
                          border-radius: 8rpx;
                          font-size: 20rpx;
                          color: #fff;

                          &.step-new {
                            background-color: #909399;
                          }

                          &.step-progress {
                            background-color: #409eff;
                          }

                          &.step-completed {
                            background-color: #67c23a;
                          }

                          &.step-default {
                            background-color: #999;
                          }
                        }

                        .step-actions {
                          display: flex;
                          align-items: center;

                          .step-action-btn {
                            padding: 6rpx 12rpx;
                            border-radius: 6rpx;
                            font-size: 20rpx;
                            color: #fff;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            white-space: nowrap;

                            &.start-btn {
                              background-color: #409eff;

                              &:hover {
                                background-color: #66b1ff;
                              }

                              &.loading {
                                background-color: #a0cfff;
                                cursor: not-allowed;
                                opacity: 0.8;
                              }
                            }

                            &.complete-btn {
                              background-color: #67c23a;

                              &:hover {
                                background-color: #85ce61;
                              }

                              &.loading {
                                background-color: #b3e19d;
                                cursor: not-allowed;
                                opacity: 0.8;
                              }
                            }

                            &.defect-btn {
                              background-color: #e6a23c;
                              margin-left: 8rpx;

                              &:hover {
                                background-color: #ebb563;
                              }

                              &.loading {
                                background-color: #f3d19e;
                                cursor: not-allowed;
                                opacity: 0.8;
                              }
                            }

                            &.material-btn {
                              background-color: #ff9500;
                              margin-right: 8rpx;

                              &:hover {
                                background-color: #ffad33;
                              }

                              &:active {
                                background-color: #e6850e;
                              }
                            }

                            &.storage-btn {
                              background-color: #9c27b0;
                              margin-right: 8rpx;

                              &:hover {
                                background-color: #ba68c8;
                              }

                              &:active {
                                background-color: #7b1fa2;
                              }
                            }

                            .loading-text {
                              color: #fff;
                              opacity: 0.9;
                            }

                            &:active {
                              opacity: 0.7;
                            }
                          }
                        }
                      }
                    }

                    .step-details {
                      .step-assignee,
                      .step-expected,
                      .step-completed {
                        display: block;
                        font-size: 20rpx;
                        color: #666;
                        margin-bottom: 2rpx;
                      }

                      .step-completed {
                        color: #67c23a;
                      }
                    }
                  }
                }
              }

              .no-steps {
                text-align: center;
                padding: 20rpx 0;

                .no-steps-text {
                  font-size: 22rpx;
                  color: #999;
                }
              }
            }
          }
        }
      }

      /* 库存信息按钮样式 */
      .inventory-button {
        display: flex;
        align-items: center;
        padding: 8rpx 12rpx;
        background-color: #f0f8ff;
        border-radius: 8rpx;
        border: 1rpx solid #e1f3ff;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-right: 12rpx;

        &:hover {
          background-color: #e6f4ff;
          border-color: #91d5ff;
        }

        .inventory-label {
          font-size: 22rpx;
          color: #1890ff;
          font-weight: 500;
          margin-right: 6rpx;
        }

        .inventory-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24rpx;
          height: 24rpx;

          .info-icon {
            font-size: 20rpx;
            color: #1890ff;
          }

          &.loading {
            animation: spin 1s linear infinite;
          }
        }
      }



    }

    /* 卡片底部 */
    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: 1rpx solid #f0f0f0;
      padding-top: 16rpx;
      margin-top: 20rpx;

      .create-time {
        font-size: 24rpx;
        color: #999;
      }

      .view-detail {
        display: flex;
        align-items: center;

        .detail-text {
          font-size: 24rpx;
          color: #007aff;
          margin-right: 4rpx;
        }

        .arrow {
          font-size: 20rpx;
          color: #007aff;
        }
      }
    }
    }
  }

  /* 加载更多 */
  .load-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;

    .load-more-text {
      margin-left: 16rpx;
      font-size: 26rpx;
      color: #999;
    }
  }

  /* 没有更多数据 */
  .no-more {
    display: flex;
    justify-content: center;
    padding: 40rpx 0;

    .no-more-text {
      font-size: 26rpx;
      color: #ccc;
    }
  }
  }
}

/* 库存对话框样式 */
.inventory-popup-container {
  width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 24rpx 16rpx 24rpx;
  border-bottom: 1rpx solid #e8e8e8;

  .popup-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    flex: 1;
  }

  .popup-close {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #f5f5f5;
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background-color: #e8e8e8;
    }

    .close-icon {
      font-size: 36rpx;
      color: #666;
      line-height: 1;
    }
  }
}

.inventory-dialog-content {
  padding: 20rpx 24rpx;
  max-height: 600rpx;
  overflow-y: auto;

  .dialog-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx 0;

    .loading-text {
      margin-top: 20rpx;
      font-size: 28rpx;
      color: #999;
    }
  }

  .dialog-error {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60rpx 20rpx;

    .error-text {
      font-size: 28rpx;
      color: #f56c6c;
      text-align: center;
    }
  }

  .dialog-inventory-list {
    .dialog-inventory-item {
      margin-bottom: 24rpx;
      padding: 16rpx;
      background-color: #f8f9fa;
      border-radius: 12rpx;
      border: 1rpx solid #e9ecef;

      &:last-child {
        margin-bottom: 0;
      }

      .dialog-warehouse-header {
        margin-bottom: 12rpx;
        padding-bottom: 8rpx;
        border-bottom: 1rpx solid #dee2e6;

        .dialog-warehouse-name {
          font-size: 30rpx;
          font-weight: 600;
          color: #333;
        }
      }

      .dialog-stock-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .dialog-stock-label {
          font-size: 26rpx;
          color: #666;
          font-weight: 500;
        }

        .dialog-stock-value {
          font-size: 26rpx;
          color: #333;
          font-weight: 600;
        }
      }
    }
  }
}

.popup-footer {
  padding: 16rpx 24rpx 24rpx 24rpx;
  border-top: 1rpx solid #e8e8e8;

  .popup-button {
    width: 100%;
    height: 80rpx;
    background-color: #007aff;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background-color: #0056cc;
    }

    &:active {
      background-color: #004499;
    }

    .button-text {
      font-size: 30rpx;
      color: #fff;
      font-weight: 500;
    }
  }
}

/* 动画效果 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式优化 - 小屏幕适配 */
@media screen and (max-width: 750rpx) {
  .my-task-page {
    .task-list {
      padding: 16rpx 12rpx;
    }

    .order-cards {
      .order-card {
        padding: 16rpx;

        .card-header {
          .order-info {
            .order-code {
              font-size: 28rpx;
              min-width: 240rpx;
            }
          }
        }

        .card-content {
          .stats-info {
            .stat-item {
              padding: 0 2rpx;

              .stat-label {
                font-size: 20rpx;
              }

              .stat-value {
                font-size: 24rpx;
              }
            }
          }

          .products-preview {
            .product-list {
              .product-item {
                .product-header {
                  .product-actions {
                    gap: 8rpx;

                    .inventory-button {
                      padding: 6rpx 8rpx;
                      margin-right: 8rpx;

                      .inventory-label {
                        font-size: 20rpx;
                        margin-right: 4rpx;
                      }

                      .inventory-icon {
                        width: 20rpx;
                        height: 20rpx;

                        .info-icon {
                          font-size: 18rpx;
                        }
                      }
                    }

                    .bom-button {
                      padding: 6rpx 8rpx;

                      .bom-text {
                        font-size: 22rpx;
                      }
                    }
                  }
                }

                .product-details {
                  .step-tasks {
                    .step-list {
                      .step-item {
                        .step-header {
                          .step-right {
                            .step-actions {
                              .step-action-btn {
                                padding: 4rpx 8rpx;
                                font-size: 18rpx;
                                margin-left: 6rpx;

                                &.material-btn {
                                  margin-right: 6rpx;
                                  margin-left: 0;
                                }

                                &.storage-btn {
                                  margin-right: 6rpx;
                                  margin-left: 0;
                                }

                                &.defect-btn {
                                  margin-left: 6rpx;
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>